/* import React from "react";
import {createBrowserRouter ,RouterProvider} from 'react-router-dom'
import ContactUs from "./ContactUs";
import Homepage from "./Homepage";


const router  = createBrowserRouter ([
   {
   path: '/',
   element: <Homepage/>,

},
{
    path: '/contact',
    element: <ContactUs/>,
 
 },
]);


const Router =() => {
    return <RouterProvider router={router} />;

};

export default Router; */