@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600&display=swap');

.wrapper :global(*) {
    font-family: 'Cairo', sans-serif;
}

.chatContainer {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.floatingButton {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #818cf8 0%, #6366f1 100%);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 26px;
    cursor: pointer;
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.4),
                0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    overflow: hidden;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 8px 32px rgba(99, 102, 241, 0.4),
                   0 2px 8px rgba(0, 0, 0, 0.1);
    }
    50% {
        box-shadow: 0 12px 40px rgba(99, 102, 241, 0.6),
                   0 4px 12px rgba(0, 0, 0, 0.2);
    }
    100% {
        box-shadow: 0 8px 32px rgba(99, 102, 241, 0.4),
                   0 2px 8px rgba(0, 0, 0, 0.1);
    }
}

.floatingButton:hover {
    transform: scale(1.1) rotate(8deg);
    background: linear-gradient(135deg, #a5b4fc 0%, #818cf8 100%);
}

.floatingButton:active {
    transform: scale(0.95);
}

.chatWrapper {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 340px;
    height: 600px;
    background: linear-gradient(135deg, #f5f3ff 0%, #ede9fe 100%);
    border: 1px solid rgba(99, 102, 241, 0.1);
    border-radius: 24px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15),
                0 10px 24px rgba(99, 102, 241, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: slideUp 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    backdrop-filter: blur(10px);
}

.chatHeader {
    background: linear-gradient(135deg, #818cf8 0%, #6366f1 100%);
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: white;
    border-bottom: 1px solid rgba(99, 102, 241, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    animation: slideDown 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    letter-spacing: 0.5px;
    font-weight: 500;
}

.chatTitle {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    flex-grow: 1;
    text-align: right;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.socialButtons {
    display: flex;
    gap: 12px;
}

.socialButton {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    font-size: 20px;
    text-decoration: none;
    backdrop-filter: blur(5px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.socialButton:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.whatsappButton {
    background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
}

.whatsappButton:hover {
    background: linear-gradient(135deg, #128C7E 0%, #075E54 100%);
}

.messengerButton {
    background: linear-gradient(135deg, #0099FF 0%, #0066FF 100%);
}

.messengerButton:hover {
    background: linear-gradient(135deg, #0066FF 0%, #0033CC 100%);
}

.messagesContainer {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
    scroll-behavior: smooth;
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
}

.messagesContainer::-webkit-scrollbar {
    width: 8px;
}

.messagesContainer::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

.messagesContainer::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.messagesContainer::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.25);
}

.messageUser {
    background: linear-gradient(135deg, #818cf8 0%, #6366f1 100%);
    color: #ffffff;
    align-self: flex-end;
    padding: 12px 16px;
    border-radius: 18px 18px 4px 18px;
    max-width: 80%;
    word-wrap: break-word;
    line-height: 1.5;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
    animation: messageSlideLeft 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    transform-origin: right bottom;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.messageAssistant {
    background: linear-gradient(135deg, #9ebeff 0%, #3d8dfd 100%);
    color: #ffffffee;
    align-self: flex-start;
    padding: 12px 16px;
    border-radius: 18px 18px 18px 4px;
    max-width: 80%;
    word-wrap: break-word;
    line-height: 1.5;
    font-size: 20px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.1);
    animation: messageSlideRight 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    letter-spacing: 0.3px;
}

.inputContainer {
    padding: 10px;
    background: linear-gradient(180deg, #f5f3ff 0%, #ffffff 100%);
    border-top: 1px solid rgba(99, 102, 241, 0.1);
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    gap: 12px;
    animation: slideUp 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.input {
    flex: 1;
    padding: 12px 18px;
    border: 2px solid rgba(99, 102, 241, 0.2);
    border-radius: 20px;
    outline: none;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
    font-weight: 400;
    letter-spacing: 0.2px;
}

.input:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);
}

.input::placeholder {
    color: #a5b4fc;
    font-weight: 400;
}

.sendButton {
    padding: 12px;
    background: linear-gradient(135deg, #818cf8 0%, #6366f1 100%);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    width: 52px;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3),
                0 0 0 2px rgba(255, 255, 255, 0.4) inset;
    position: relative;
    overflow: hidden;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

.sendButton:hover:not(:disabled) {
    transform: scale(1.05);
    background: linear-gradient(135deg, #a5b4fc 0%, #818cf8 100%);
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4),
                0 0 0 2px rgba(255, 255, 255, 0.6) inset;
}

.sendButton:active:not(:disabled) {
    transform: scale(0.98);
    transition: all 0.1s ease;
}

.sendButton:disabled {
    background: linear-gradient(135deg, #c7d2fe 0%, #a5b4fc 100%);
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes messageSlideLeft {
    from {
        opacity: 0;
        transform: translateX(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes messageSlideRight {
    from {
        opacity: 0;
        transform: translateX(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@media (max-width: 768px) {
    .chatWrapper {
        width: 90vw;
        right: 5vw;
        height: 80vh;
        bottom: 85px;
    }

    .floatingButton {
        width: 50px;
        height: 50px;
        font-size: 22px;
    }

    .chatTitle {
        font-size: 16px;
    }

    .input {
        font-size: 14px;
        padding: 10px 16px;
    }

    .sendButton {
        width: 48px;
        height: 48px;
        font-size: 18px;
        padding: 12px;
        margin: 0 2px;
    }
}

@media (max-width: 600px) {
    .chatWrapper {
        width: 92vw;
        right: 4vw;
        max-height: 85vh;
        bottom: 85px;
    }
    
    .inputContainer {
        padding: 15px;
    }
    
    .messageUser,
    .messageAssistant {
        max-width: 85%;
        font-size: 15px;
    }

    .input {
        font-size: 16px;
        font-weight: 500;
        padding: 12px 16px;
    }

    .chatHeader {
        padding: 15px;
    }

    .chatTitle {
        font-size: 18px;
    }

    .socialButton {
        width: 36px;
        height: 36px;
        font-size: 18px;
    }

    .sendButton {
        width: 46px;
        height: 46px;
        font-size: 18px;
        padding: 10px;
        margin: 0 1px;
    }
}

.messageAssistant :global(h2),
.messageAssistant :global(h3),
.messageAssistant :global(strong),
.messageAssistant :global(p) {
    color: inherit;
    background: none;
    border: none;
}

.markdownContent {
    font-size: 15px;
}

.markdownContent h2,
.markdownContent h3 {
    font-size: 1.2em;
    margin-bottom: 10px;
    font-weight: 600;
}

.markdownContent p {
    margin: 8px 0;
}

.markdownContent strong {
    font-weight: 600;
}