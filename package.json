{"name": "shipping-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-google-maps/api": "^2.19.3", "bootstrap": "^5.3.3", "leaflet": "^1.9.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-leaflet": "^4.2.1", "react-markdown": "^9.0.1"}, "devDependencies": {"@eslint/js": "^9.8.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.8.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "react-router-dom": "^6.26.1", "typescript": "^5.5.3", "typescript-eslint": "^8.0.0", "vite": "^5.4.11"}}