"use client"
import React, { useEffect, useState, useRef, KeyboardEvent, ChangeEvent } from 'react';
import ReactMarkdown from 'react-markdown';
import styles from '../styles.module.css';

interface Message {
    role: 'user' | 'assistant';
    content: string;
    id?: number;
}

interface ApiResponse {
    candidates: Array<{
        content: {
            parts: Array<{
                text: string;
            }>;
        };
    }>;
}

const Chat: React.FC = () => {
    const [messages, setMessages] = useState<Message[]>([]);
    const [input, setInput] = useState<string>('');
    const [isSending, setIsSending] = useState<boolean>(false);
    const [isChatOpen, setIsChatOpen] = useState<boolean>(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = (): void => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        const firstMsg: Message = { 
            role: 'assistant', 
            content: 'مرحباً بكم في أطلس! 🌟 يسعدنا تواجدكم هنا. كيف يمكنني مساعدتكم اليوم؟ 🤔💬' 
        };
        setMessages([firstMsg]);
    }, []);

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    const sendMessage = async (msg: string): Promise<void> => {
        if (!msg.trim() || isSending) return;
        setIsSending(true);

        try {
            const userMessage: Message = { role: 'user', content: msg, id: Date.now() };
            setMessages(prev => [...prev, userMessage]);

            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ prompt: msg }),
            });

            if (!response.ok) {
                throw new Error(`Error: ${response.status} ${response.statusText}`);
            }

            const data: ApiResponse = await response.json();
            const assistantContent = data.candidates[0].content.parts[0].text.trim();
            const assistantMessage: Message = { 
                role: 'assistant', 
                content: assistantContent, 
                id: Date.now() 
            };

            setMessages(prev => [...prev, assistantMessage]);
        } catch (error) {
            console.log('Failed to fetch assistant response:', error);
            const errorMessage: Message = { 
                role: 'assistant', 
                content: 'عذرًا، حدث خطأ ما. يرجى المحاولة مرة أخرى.', 
                id: Date.now() 
            };
            setMessages(prev => [...prev, errorMessage]);
        } finally {
            setIsSending(false);
        }
    };

    const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>): void => {
        if (e.key === 'Enter' && !e.shiftKey && !isSending && input.trim()) {
            e.preventDefault();
            sendMessage(input);
            setInput('');
        }
    };

    return (
        <div className={styles.chatContainer}>
            <button className={styles.floatingButton} onClick={() => setIsChatOpen(!isChatOpen)}>
                💬
            </button>

            {isChatOpen && (
                <div className={styles.chatWrapper}>
                    <div className={styles.chatHeader}>
                        <div className={styles.socialButtons}>
                            <a 
                                href="https://wa.me/0911008152" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className={`${styles.socialButton} ${styles.whatsappButton}`}
                            >
                                <svg viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413Z"/>
                                </svg>
                            </a>
                            <a 
                                href="https://www.facebook.com/albariq.aldaayim/" 
                                onClick={(e: React.MouseEvent<HTMLAnchorElement>) => {
                                    e.preventDefault();
                                    window.open('your-messenger-link', '_blank');
                                }}
                                className={`${styles.socialButton} ${styles.messengerButton}`}
                            >
                                <svg viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
                                    <path d="M12 2C6.477 2 2 6.145 2 11.243c0 2.936 1.444 5.564 3.741 7.288v3.803l3.41-1.869c.909.251 1.867.386 2.849.386 5.523 0 10-4.145 10-9.243C22 6.145 17.523 2 12 2zm1.193 12.322l-2.55-2.72-4.964 2.72 5.467-5.8 2.611 2.72 4.904-2.72-5.468 5.8z"/>
                                </svg>
                            </a>
                        </div>
                        <h1 className={styles.chatTitle}>المساعد الافتراضي</h1>
                    </div>
                    <div className={styles.messagesContainer}>
                        {messages.map((msg, index) => (
                            <div
                                key={msg.id || index}
                                className={msg.role === 'user' ? styles.messageUser : styles.messageAssistant}
                            >
                                <div className={styles.markdownContent}>
                                    <ReactMarkdown>
                                        {msg.content}
                                    </ReactMarkdown>
                                </div>
                            </div>
                        ))}
                        <div ref={messagesEndRef} />
                    </div>
                    <div className={styles.inputContainer}>
                        <input
                            type="text"
                            value={input}
                            onChange={(e: ChangeEvent<HTMLInputElement>) => setInput(e.target.value)}
                            onKeyPress={handleKeyPress}
                            disabled={isSending}
                            placeholder="قم بكتابة الرسالة هنا..."
                            className={styles.input}
                            dir="rtl"
                        />
                        <button
                            onClick={() => {
                                if (input.trim()) {
                                    sendMessage(input);
                                    setInput('');
                                }
                            }}
                            disabled={isSending || !input.trim()}
                            className={isSending ? `${styles.sendButton} ${styles.disabled}` : styles.sendButton}
                        >
                            {isSending ? '🤖' : '➤'}
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Chat;
