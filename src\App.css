
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&family=Caveat:wght@400..700&display=swap');
* {
  font-family: "Cairo", sans-serif;
  font-optical-sizing: auto;
  font-weight: 100;
  font-style: normal;
  font-variation-settings:
    "slnt" 0;
}


/* custom.css */
.navbar-nav .nav-item .nav-link:hover {
  color: black; 
  background-color: #f0f0f0;
}
.d-inline-block {
 /* border-radius: 50%;*/
  margin-right: 10px;
}

.body {
  height: 100vh;
  width: 100vh;
}
.carousel {
  margin-bottom: 4rem;

}
.carousel {
  position: relative;
}
*, ::after, ::before {
  box-sizing: border-box;
}
user agent stylesheetdiv {
  display: block;
  unicode-bidi: isolate;
}
.body {
  padding-top: 3rem;
  padding-bottom: 3rem;
  color: rgb(var(--bs-tertiary-color-rgb));
}
.body {
  margin: 0;
  font-family: var(--bs-body-font-family);
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  color: var(--bs-body-color);
  text-align: var(--bs-body-text-align);
  -webkit-tap-highlight-color: transparent;
}
:root {
  --bs-breakpoint-xs: 0;
  --bs-breakpoint-sm: 576px;
  --bs-breakpoint-md: 768px;
  --bs-breakpoint-lg: 992px;
  --bs-breakpoint-xl: 1200px;
  --bs-breakpoint-xxl: 1400px;
}
[data-bs-theme=dark] {
  color-scheme: dark;
  --bs-body-color: #dee2e6;
  --bs-body-color-rgb: 222, 226, 230;
  --bs-body-bg: #212529;
  --bs-body-bg-rgb: 33, 37, 41;
  --bs-emphasis-color: #fff;
  --bs-emphasis-color-rgb: 255, 255, 255;
  --bs-secondary-color: rgba(222, 226, 230, 0.75);
  --bs-secondary-color-rgb: 222, 226, 230;
  --bs-secondary-bg: #343a40;
  --bs-secondary-bg-rgb: 52, 58, 64;
  --bs-tertiary-color: rgba(222, 226, 230, 0.5);
  --bs-tertiary-color-rgb: 222, 226, 230;
  --bs-tertiary-bg: #2b3035;
  --bs-tertiary-bg-rgb: 43, 48, 53;
  --bs-primary-text-emphasis: #6ea8fe;
  --bs-secondary-text-emphasis: #a7acb1;
  --bs-success-text-emphasis: #75b798;
  --bs-info-text-emphasis: #6edff6;
  --bs-warning-text-emphasis: #ffda6a;
  --bs-danger-text-emphasis: #ea868f;
  --bs-light-text-emphasis: #f8f9fa;
  --bs-dark-text-emphasis: #dee2e6;
  --bs-primary-bg-subtle: #031633;
  --bs-secondary-bg-subtle: #161719;
  --bs-success-bg-subtle: #051b11;
  --bs-info-bg-subtle: #032830;
  --bs-warning-bg-subtle: #332701;
  --bs-danger-bg-subtle: #2c0b0e;
  --bs-light-bg-subtle: #343a40;
  --bs-dark-bg-subtle: #1a1d20;
  --bs-primary-border-subtle: #084298;
  --bs-secondary-border-subtle: #41464b;
  --bs-success-border-subtle: #0f5132;
  --bs-info-border-subtle: #087990;
  --bs-warning-border-subtle: #997404;
  --bs-danger-border-subtle: #842029;
  --bs-light-border-subtle: #495057;
  --bs-dark-border-subtle: #343a40;
  --bs-heading-color: inherit;
  --bs-link-color: #6ea8fe;
  --bs-link-hover-color: #8bb9fe;
  --bs-link-color-rgb: 110, 168, 254;
  --bs-link-hover-color-rgb: 139, 185, 254;
  --bs-code-color: #e685b5;
  --bs-highlight-color: #dee2e6;
  --bs-highlight-bg: #664d03;
  --bs-border-color: #495057;
  --bs-border-color-translucent: rgba(255, 255, 255, 0.15);
  --bs-form-valid-color: #75b798;

}

:root {
  --docsearch-primary-color: #5468ff;
  --docsearch-text-color: #1c1e21;
  --docsearch-spacing: 12px;
  --docsearch-icon-stroke-width: 1.4;
  --docsearch-highlight-color: var(--docsearch-primary-color);
  --docsearch-muted-color: #969faf;
  --docsearch-container-background: rgba(101,108,133,0.8);
  --docsearch-logo-color: #5468ff;
  --docsearch-modal-width: 560px;
  --docsearch-modal-height: 600px;
  --docsearch-modal-background: #f5f6f7;
  --docsearch-modal-shadow: inset 1px 1px 0 0 hsla(0,0%,100%,0.5), 0 3px 8px 0 #555a64;
  --docsearch-searchbox-height: 56px;
  --docsearch-searchbox-background: #ebedf0;
  --docsearch-searchbox-focus-background: #fff;
  --docsearch-searchbox-shadow: inset 0 0 0 2px var(--docsearch-primary-color);
  --docsearch-hit-height: 56px;
  --docsearch-hit-color: #444950;
  --docsearch-hit-active-color: #fff;
  --docsearch-hit-background: #fff;
  --docsearch-hit-shadow: 0 1px 3px 0 #d4d9e1;
  --docsearch-key-gradient: linear-gradient(-225deg,#d5dbe4,#f8f8f8);
  --docsearch-key-shadow: inset 0 -2px 0 0 #cdcde6, inset 0 0 1px 1px #fff, 0 1px 2px 1px rgba(30,35,90,0.4);
  --docsearch-key-pressed-shadow: inset 0 -2px 0 0 #cdcde6, inset 0 0 1px 1px #fff, 0 1px 1px 0 rgba(30,35,90,0.4);
  --docsearch-footer-height: 44px;
  --docsearch-footer-background: #fff;
  --docsearch-footer-shadow: 0 -1px 0 0 #e0e3e8, 0 -3px 6px 0 rgba(69,98,155,0.12);
}
*, ::after, ::before {
  box-sizing: border-box;
}
*, ::after, ::before {
  box-sizing: border-box;
}
.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  justify-content: center;
  padding: 0;
  margin-right: 15%;
  margin-bottom: 1rem;
  margin-left: 15%;
}
.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
*, ::after, ::before {
  box-sizing: border-box;
}

.div {
  display: block;
  unicode-bidi: isolate;
}
.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transition: transform .6s ease-in-out;
  
}
.bd-placeholder-img {
  font-size: 1.125rem;
  text-anchor: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.img, svg {
  vertical-align: middle;
}
*, ::after, ::before {
  box-sizing: border-box;
}
.bd-placeholder-img {
  font-size: 1.125rem;
  text-anchor: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.container, .container-fluid, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  width: 100%;
  padding-right: calc(var(--bs-gutter-x)* .5);
  padding-left: calc(var(--bs-gutter-x)* .5);
  margin-right: auto;
  margin-left: auto; }
  .carousel-item {
    height: 32rem;
}
.carousel-caption.start{
  text-align: center;
  color: #ffffff;
   top: 43%;
  position: absolute;
  border: #031633;
 background-color: rgba(0,0,0, 0.3);
 background-size: contain;
 background-repeat: no-repeat;
 height: 15%;
 border-radius: 0%;

  
}

.icon-square {
  width: 3rem;
  height: 3rem;
  border-radius: 0.75rem;

}
.pb-2 {
  padding-bottom: .5rem !important;
  text-align: center;

  
}
.fs-10{
  text-align: right;

}
.fs-11{
  text-align: right;
 
}
.carousel-caption.rest{
  text-align: center;
  color: #ffffff;
   top: 37%;
  position: absolute;
  background-color: rgba(0,0,0, 0.3);
 background-size: contain;
 background-repeat: no-repeat;
 height: 39%;
 border-radius: 0%;
}
.bd-placeholder-img{
  background-color: rgba(0,0,0, 0.);
}
.icon-square{
  text-align: center;
  justify-items: center;
}
.fs-2{
  text-align: center;
}
.card {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;

  
}



.flex-column{
   background-color: rgba(0, 0, 0, 0.5);
}

/* Example class for a specific image */
.cardone {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-image: url('../close-up-detail-view-of-cargo-cart-trolley-full-wi-2024-02-09-15-22-45-utc.jpg');

}

.cardtwo {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-image: url('../vecteezy_delivery-truck-loaded-with-cardboard-boxes-logistics_25870607.jpeg');
}
.cardthree {
  position: relative;
  width: 100%;
  height: 100%;
   
  background-size: cover;
  background-position: center;
  background-image: url('../container-ship-cargo-freight-shipping-maritime-gl-2023-11-27-05-31-32-utc.jpg');
}


/* Additional styles for the card */
.text-shadow-1 {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}
.pt-5{
  text-align: center;
  opacity: 0.9;
}
.fs-10{
  margin-top: 10px;
  margin-bottom: 10px;
}



.contact-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 20px;
  max-width: 1200px;
  margin: auto;
  margin-top: 20px;
}

.map-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.map-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.contact-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  text-align: center;
}

.contact-info h2 {
  margin-top: 0;
  color: #333;
}

.contact-info p {
  margin: 10px 0;
  color: #555;
}

.phone-number {
  color: #007BFF;
  text-decoration: none;
}

.phone-number:hover {
  text-decoration: underline;
}

a {
  color: #007BFF;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}


@media (max-width: 768px) {
  .contact-section {
      grid-template-columns: 1fr;
  }
}
.fs-11{
  margin-top: 10px;
  margin-bottom: 10px;
}






.footer{
  max-width: 1350px;
  overflow: hidden;
}
.footer .container .row{
  display: flex;
  justify-content: center;
  
}
.footer .container{
  background-color: #0d4175;
  color: #ffffff;
  padding: 2rem 0;
  font-size: 0.9rem;
  border-radius: 20px;
  
  /*background-color: #343a40;*/
}
.footer-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.footer-links {
  list-style-type: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: #ffffff;
  text-decoration: none;
}

.footer-links a:hover {
  text-decoration: underline;
}

.footer p {
  margin-bottom: 0.5rem;
}


.card:hover{
  cursor: pointer;
}
.col-md-4{
  text-align: center;
}

.container1{
 display: grid;
 grid-template-columns: repeat(auto-fit ,minmax(300px,1fr));
 height: 80vh;
 padding-right: 3rem !important;
 padding-left: 3rem !important;
  padding-bottom: 5%;
}  
.photo1{
background-image: url(../containers-on-a-vessel-global-market-cargo-shipp-2023-11-27-05-13-43-utc.jpg);
background-size: cover;
background-position: center;
}
.photo2{
  background-image: url(../vecteezy_big-transport-ship-loaded-with-containers-with-goods-on-the_27530540.jpg);
  background-size: cover;
  background-position: center;
  }
.text{
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #f4f4f4;
}
.text-content{
  text-align: center;
}
.number-content{
word-wrap:break-word;
}





.container2 {
   position: relative; 
/* width: 100%;  */
 height: 80vh; 
  margin-right: 3rem !important;
  margin-left: 3rem !important;
  
  background-size: cover; 
  background-position: center;
}


.text-shipping {
  text-align: center;
  margin-bottom: 20px; 
  color: #f4f4f4;
  
  top: 0;
  left: 0;
  width: 100%;
  height: 80vh;
  background-image: url("../Global_network_generated.jpg");
  z-index: 1; 
  background-size: cover; 
  background-position: center;
  background-repeat: no-repeat;
}


.photo-container {
   
  width: 100%;
   border-radius: 20px;
  background-image: url("../vecteezy_startup-sme-small-business-entrepreneur-of-freelance-asian_12497537.jpg"); /* Set the path to your image */
  background-size: cover; 
  background-position: center;
  min-height: 150px;
}






.text-shipping1{
  text-align: center;
  color: #ffffff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.9);
  margin-top: 75px;
}

.row1{
  /*max-width: 1350px;
  display: flex;
  gap: 20px;*/
  overflow: hidden;
  max-width: 1420px;
  flex-wrap: wrap;
  display: flex;
  align-items: stretch;
  justify-content: center;
  gap: 40px;
}
.row1 div{
  overflow: hidden;
  max-width: 380px;
  
  
}
  
/*.row div div{
  max-width: 300px;
  max-height: 350px;
}*/
@media (max-width: 1000px){
 .row{
  max-width: fit-content;

 }
 .row div{
  max-width: fit-content;
 }
 
}
@media (max-width: 768px) {
  .footer {
      text-align: center;
  }

  .footer .row {
      text-align: center;
  }
  .container2{
    max-width: 600px;
    display: flex;
    justify-content: center;
  }
  .container2 .text-shipping {
    width: 100%;
    height: 100%;
  }
  .container1 .number-content{
    margin: auto;
  }
  .container1 .photo2{
    min-height: 160px;
  }
}
@media (max-width: 900px){
  .container2{
    max-width: 600px;
    display: flex;
    justify-content: center;
  }
  .container2 .text-shipping {
    width: 100%;
    height: 100%;
  }
}